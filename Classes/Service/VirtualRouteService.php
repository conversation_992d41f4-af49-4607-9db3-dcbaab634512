<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\TemplateResolutionService;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Virtual Route Service
 * 
 * Manages virtual route state and provides utilities for virtual route detection
 * and page data manipulation.
 */
class VirtualRouteService
{
    private ?array $currentVirtualRoute = null;
    protected ?TemplateResolutionService $templateResolutionService = null;

    public function __construct(TemplateResolutionService $templateResolutionService = null)
    {
        $this->templateResolutionService = $templateResolutionService;
    }

    /**
     * Set the current virtual route data
     */
    public function setVirtualRoute(array $routeData): void
    {
        $this->currentVirtualRoute = $routeData;
    }

    /**
     * Get the current virtual route data
     */
    public function getCurrentVirtualRoute(): ?array
    {
        return $this->currentVirtualRoute;
    }

    /**
     * Clear the current virtual route
     */
    public function clearVirtualRoute(): void
    {
        $this->currentVirtualRoute = null;
    }

    /**
     * Check if we're currently processing a virtual route
     */
    public function isVirtualRoute(): bool
    {
        return $this->currentVirtualRoute !== null;
    }

    /**
     * Detect if a path matches a virtual route pattern
     */
    public function detectVirtualRoute(string $path, Site $site): ?array
    {
        error_log('VirtualRouteService: detectVirtualRoute called with path: ' . $path);

        $pathParts = explode('/', trim($path, '/'));
        error_log('VirtualRouteService: Path parts: ' . json_encode($pathParts));

        if (count($pathParts) < 2) {
            error_log('VirtualRouteService: Path has less than 2 parts, returning null');
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ist-var")
        $routeSlug = end($pathParts);
        error_log('VirtualRouteService: Route slug: ' . $routeSlug);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);
        error_log('VirtualRouteService: Landing page path: ' . $landingPagePath);

        // Ensure path starts with /
        $searchPath = '/' . ltrim($landingPagePath, '/');
        $searchPath = str_replace($site->getBase()->getPath(), '', $searchPath);
        $searchPathLP = ltrim($searchPath, '/');

        error_log('VirtualRouteService: Search path: ' . $searchPath);
        error_log('VirtualRouteService: Search path LP: ' . $searchPathLP);
        error_log('VirtualRouteService: Site base path: ' . $site->getBase()->getPath());

        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($searchPath, $site);
        error_log('VirtualRouteService: Landing page found: ' . ($landingPageData ? 'YES (uid: ' . $landingPageData['uid'] . ')' : 'NO'));

        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $fullRouteSlug = "{$searchPathLP}/{$routeSlug}";
        error_log('VirtualRouteService: Looking for flight route with slug: ' . $fullRouteSlug . ' in landing page: ' . $landingPageData['uid']);
        $flightRoute = $this->findFlightRoute($fullRouteSlug, $landingPageData['uid']);
        error_log('VirtualRouteService: Flight route found: ' . ($flightRoute ? 'YES (uid: ' . $flightRoute['uid'] . ')' : 'NO'));

        if (!$flightRoute) {
            return null;
        }

        // Resolve template page UID using new service (with fallback)
        $templatePageUid = $landingPageData['tx_landingpages_template_page'];
        if ($this->templateResolutionService) {
            $templatePageUid = $this->templateResolutionService->resolveTemplatePageForRoute(
                $landingPageData['uid'],
                $flightRoute['origin_type'] ?? 'airport',
                $flightRoute['destination_type'] ?? 'airport'
            );
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => $routeSlug,
            'originalPath' => $path,
            'templatePageUid' => $templatePageUid
        ];
    }

    /**
     * Create virtual page data by merging landing page and template page
     */
    public function createVirtualPageData(array $landingPage, array $templatePage, array $flightRoute, string $originalPath): array
    {
        // Start with the landing page as base
        $virtualPage = $landingPage;

        // Override with template page content-related fields
        $virtualPage['title'] = $templatePage['title'] ?? $landingPage['title'];
        $virtualPage['subtitle'] = $templatePage['subtitle'] ?? $landingPage['subtitle'];
        $virtualPage['description'] = $templatePage['description'] ?? $landingPage['description'];
        $virtualPage['keywords'] = $templatePage['keywords'] ?? $landingPage['keywords'];
        $virtualPage['seo_title'] = $templatePage['seo_title'] ?? $landingPage['seo_title'];

        // Process placeholders in the virtual page data
        $virtualPage = $this->processPlaceholdersInPageData($virtualPage, $flightRoute);

        // Set virtual page properties
        $virtualPage['slug'] = '/' . ltrim($originalPath, '/');
        $virtualPage['is_virtual'] = true;
        $virtualPage['tx_landingpages_template_page'] = $templatePage['uid'];
        $virtualPage['flight_route_uid'] = $flightRoute['uid'];

        return $virtualPage;
    }

    /**
     * Process placeholders in page data
     */
    public function processPlaceholdersInPageData(array $pageData, array $flightRoute): array
    {
        $placeholders = [
            // Existing placeholders (maintain backward compatibility)
            '[_origin_code_]' => $flightRoute['origin_code'] ?? '',
            '[_origin_name_]' => $flightRoute['origin_name'] ?? '',
            '[_origin_type_]' => $flightRoute['origin_type'] ?? '',
            '[_destination_code_]' => $flightRoute['destination_code'] ?? '',
            '[_destination_name_]' => $flightRoute['destination_name'] ?? '',
            '[_destination_type_]' => $flightRoute['destination_type'] ?? '',
            '[_route_slug_]' => $flightRoute['route_slug'] ?? '',
            '[_route_]' => ($flightRoute['origin_name'] ?? '') . ' → ' . ($flightRoute['destination_name'] ?? ''),
            '[_route_dash_]' => ($flightRoute['origin_code'] ?? '') . '-' . ($flightRoute['destination_code'] ?? ''),
            '[_route_text_]' => ($flightRoute['origin_name'] ?? '') . ' to ' . ($flightRoute['destination_name'] ?? ''),

            // New type-aware placeholders
            '[_origin_]' => $this->buildTypeSpecificPlaceholder(
                $flightRoute['origin_name'] ?? '',
                $flightRoute['origin_code'] ?? '',
                $flightRoute['origin_type'] ?? ''
            ),
            '[_destination_]' => $this->buildTypeSpecificPlaceholder(
                $flightRoute['destination_name'] ?? '',
                $flightRoute['destination_code'] ?? '',
                $flightRoute['destination_type'] ?? ''
            ),
        ];

        foreach ($pageData as $field => $value) {
            if (is_string($value)) {
                $pageData[$field] = str_replace(array_keys($placeholders), array_values($placeholders), $value);
            }
        }

        return $pageData;
    }

    /**
     * Process placeholders in content
     */
    public function processPlaceholdersInContent(string $content, array $flightRoute): string
    {
        $placeholders = [
            // Existing placeholders (maintain backward compatibility)
            '[_origin_code_]' => $flightRoute['origin_code'] ?? '',
            '[_origin_name_]' => $flightRoute['origin_name'] ?? '',
            '[_origin_type_]' => $flightRoute['origin_type'] ?? '',
            '[_destination_code_]' => $flightRoute['destination_code'] ?? '',
            '[_destination_name_]' => $flightRoute['destination_name'] ?? '',
            '[_destination_type_]' => $flightRoute['destination_type'] ?? '',
            '[_route_slug_]' => $flightRoute['route_slug'] ?? '',
            '[_route_]' => ($flightRoute['origin_name'] ?? '') . ' → ' . ($flightRoute['destination_name'] ?? ''),
            '[_route_dash_]' => ($flightRoute['origin_code'] ?? '') . '-' . ($flightRoute['destination_code'] ?? ''),
            '[_route_text_]' => ($flightRoute['origin_name'] ?? '') . ' to ' . ($flightRoute['destination_name'] ?? ''),

            // New type-aware placeholders
            '[_origin_]' => $this->buildTypeSpecificPlaceholder(
                $flightRoute['origin_name'] ?? '',
                $flightRoute['origin_code'] ?? '',
                $flightRoute['origin_type'] ?? ''
            ),
            '[_destination_]' => $this->buildTypeSpecificPlaceholder(
                $flightRoute['destination_name'] ?? '',
                $flightRoute['destination_code'] ?? '',
                $flightRoute['destination_type'] ?? ''
            ),
        ];

        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }

    /**
     * Build type-specific placeholder value
     *
     * @param string $name Location name
     * @param string $code Location code
     * @param string $type Location type (airport, city, country)
     * @return string Formatted placeholder value
     */
    protected function buildTypeSpecificPlaceholder(string $name, string $code, string $type): string
    {
        if ($type === 'airport' && !empty($name) && !empty($code)) {
            return $name . ' (' . $code . ')';
        }

        return $name;
    }

    /**
     * Load template page data
     */
    public function loadTemplatePage(int $templatePageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(200, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Find landing page by path
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $siteRootPageId = $site->getRootPageId();

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($path))
            )
            ->executeQuery();

        $pages = $result->fetchAllAssociative();
        
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Find flight route by route slug and landing page
     */
    protected function findFlightRoute(string $routeSlug, int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_landingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('route_slug', $queryBuilder->createNamedParameter($routeSlug)),
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Check if page belongs to site
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        // Simple implementation - check if page is under site root
        // You might want to implement a more sophisticated check
        return true; // For now, assume all pages belong to the site
    }
}
